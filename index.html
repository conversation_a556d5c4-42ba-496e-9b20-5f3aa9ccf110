
<!DOCTYPE html>
<html lang="id">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="description" content="Profil lengkap <PERSON> - WarkopStack Developer, <PERSON>, dan <PERSON> Langit Inovasi">
  <title>Langit Cerah | Ma<PERSON></title>
  <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>☕</text></svg>">
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://unpkg.com/scrollreveal"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <script>
    tailwind.config = {
      darkMode: 'class',
      theme: {
        extend: {
          colors: {
            skyshine: {
              light: '#e0f7fa',
              DEFAULT: '#81d4fa',
              dark: '#0288d1'
            },
            kopi: {
              light: '#d7ccc8',
              DEFAULT: '#5d4037',
              dark: '#3e2723'
            }
          },
          animation: {
            fadeIn: "fadeIn 1.5s ease-in-out",
            float: "float 6s ease-in-out infinite",
            wave: "wave 2s linear infinite"
          },
          keyframes: {
            fadeIn: {
              '0%': { opacity: 0 },
              '100%': { opacity: 1 }
            },
            float: {
              '0%, 100%': { transform: 'translateY(0)' },
              '50%': { transform: 'translateY(-20px)' }
            },
            wave: {
              '0%': { transform: 'rotate(0deg)' },
              '10%': { transform: 'rotate(14deg)' },
              '20%': { transform: 'rotate(-8deg)' },
              '30%': { transform: 'rotate(14deg)' },
              '40%': { transform: 'rotate(-4deg)' },
              '50%': { transform: 'rotate(10deg)' },
              '60%': { transform: 'rotate(0deg)' },
              '100%': { transform: 'rotate(0deg)' }
            }
          }
        }
      }
    }
  </script>
  <style>
    .typing-demo {
      width: 22ch;
      animation: typing 2s steps(22), blink .5s step-end infinite alternate;
      white-space: nowrap;
      overflow: hidden;
      border-right: 3px solid;
      font-family: monospace;
    }
    
    @keyframes typing {
      from { width: 0 }
    }
    
    @keyframes blink {
      50% { border-color: transparent }
    }
    
    .rainbow-text {
      background: linear-gradient(to right, #ef5350, #f48fb1, #7e57c2, #2196f3, #26c6da, #43a047, #eeff41, #f9a825, #ff5722);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
      background-size: 400% 400%;
      animation: rainbow 15s ease infinite;
    }
    
    @keyframes rainbow {
      0% { background-position: 0% 50% }
      50% { background-position: 100% 50% }
      100% { background-position: 0% 50% }
    }
  </style>
</head>

<body class="bg-gradient-to-br from-skyshine-light via-white to-skyshine-light text-gray-800 dark:text-white font-sans animate-fadeIn dark:bg-gray-900 transition-all duration-300">
  <!-- Floating Navigation -->
  <nav class="fixed top-0 left-0 right-0 z-50 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm shadow-sm">
    <div class="container mx-auto px-6 py-3 flex justify-between items-center">
      <a href="#" class="text-xl font-bold text-skyshine-dark dark:text-skyshine flex items-center">
        <i class="fas fa-cloud-sun mr-2"></i> Langit Cerah
      </a>
      <div class="hidden md:flex space-x-6">
        <a href="#about" class="hover:text-skyshine-dark dark:hover:text-skyshine transition">Tentang</a>
        <a href="#services" class="hover:text-skyshine-dark dark:hover:text-skyshine transition">Layanan</a>
        <a href="#skills" class="hover:text-skyshine-dark dark:hover:text-skyshine transition">Keahlian</a>
        <a href="#journey" class="hover:text-skyshine-dark dark:hover:text-skyshine transition">Perjalanan</a>
        <a href="#contact" class="hover:text-skyshine-dark dark:hover:text-skyshine transition">Kontak</a>
      </div>
      <div class="flex items-center space-x-4">
        <button onclick="document.body.classList.toggle('dark')" class="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition">
          <i class="fas fa-moon dark:hidden"></i>
          <i class="fas fa-sun hidden dark:block"></i>
        </button>
        <button class="md:hidden p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition">
          <i class="fas fa-bars"></i>
        </button>
      </div>
    </div>
  </nav>

  <!-- Hero Section -->
  <header class="relative bg-skyshine dark:bg-indigo-900 overflow-hidden pt-24">
    <div class="absolute inset-0 bg-[url('https://images.unsplash.com/photo-1506748686214-e9df14d4d9d0?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80')] bg-cover bg-center opacity-30 dark:opacity-20"></div>
    <div class="relative z-10 container mx-auto py-16 text-center">
      <div class="animate-float">
        <img src="https://cdn-icons-png.flaticon.com/512/2922/2922510.png" alt="Profil Mas Daus" class="mx-auto w-40 h-40 rounded-full border-4 border-white shadow-2xl hover:shadow-skyshine transition-all duration-500 hover:scale-110">
      </div>
      <h1 class="text-5xl md:text-6xl font-extrabold tracking-wider mb-4 mt-6 rainbow-text">Mas Daus</h1>
      <div class="typing-demo mx-auto text-xl md:text-2xl text-gray-700 dark:text-gray-300 mb-6">WarkopStack Developer </div>
      
      <div class="flex justify-center space-x-4 mt-8">
        <a href="#contact" class="px-6 py-3 bg-skyshine-dark hover:bg-skyshine-darker text-white rounded-full font-medium shadow-lg transition-all hover:shadow-xl hover:-translate-y-1">
          <i class="fas fa-paper-plane mr-2"></i>Hubungi Saya
        </a>
        <a href="#services" class="px-6 py-3 bg-white dark:bg-gray-800 text-skyshine-dark dark:text-skyshine border border-skyshine-dark dark:border-skyshine rounded-full font-medium shadow-lg transition-all hover:shadow-xl hover:-translate-y-1">
          <i class="fas fa-list-alt mr-2"></i>Layanan
        </a>
      </div>
      
      <div class="flex justify-center space-x-6 mt-12 text-2xl text-gray-600 dark:text-gray-400">
        <a href="https://github.com/inidaus" target="_blank" class="hover:text-gray-900 dark:hover:text-white transition"><i class="fab fa-github"></i></a>
        <a href="https://linkedin.com" target="_blank" class="hover:text-blue-600 transition"><i class="fab fa-linkedin"></i></a>
        <a href="https://twitter.com" target="_blank" class="hover:text-sky-500 transition"><i class="fab fa-twitter"></i></a>
        <a href="https://instagram.com/inidaus" target="_blank" class="hover:text-pink-600 transition"><i class="fab fa-instagram"></i></a>
      </div>
    </div>
  </header>

  <main class="container mx-auto px-6 py-14 space-y-24">
    <!-- About Section -->
    <section id="about" class="grid md:grid-cols-2 gap-10 items-center reveal">
      <div>
        <span class="text-sm font-semibold text-skyshine-dark dark:text-skyshine uppercase tracking-wider">Tentang Saya</span>
        <h2 class="text-3xl font-bold mb-6 text-skyshine-dark dark:text-skyshine">Siapa Mas Daus?</h2>
        <p class="text-lg leading-relaxed mb-4">
          Aku adalah seorang <span class="font-semibold text-skyshine-dark dark:text-skyshine">WarkopStack Developer</span> Penjaga Warung Kopi yang jatuh cinta pada teknologi sejak pertama kali mendengar suara booting Windows 95. Di antara aroma kopi dan bunyi keyboard, aku menemukan passion untuk menyelesaikan masalah dengan kode.
        </p>
        <p class="text-lg leading-relaxed mb-6">
          Dari Sidoarjo, aku memulai perjalanan dari WordStar hingga VS Code, dari dBASE hingga Docker. Setiap error adalah cerita, setiap bug adalah petualangan. Di luar coding, aku menikmati sejuknya pegunungan dan langit berbintang sambil ngopi.
        </p>
        <div class="flex flex-wrap gap-4">
          <div class="flex items-center bg-white dark:bg-gray-800 px-4 py-2 rounded-full shadow-sm">
            <i class="fas fa-mug-hot text-kopi-default mr-2"></i>
            <span>Kopi Hitam</span>
          </div>
          <div class="flex items-center bg-white dark:bg-gray-800 px-4 py-2 rounded-full shadow-sm">
            <i class="fas fa-mountain text-green-500 mr-2"></i>
            <span>Hiking</span>
          </div>
          <div class="flex items-center bg-white dark:bg-gray-800 px-4 py-2 rounded-full shadow-sm">
            <i class="fas fa-music text-purple-500 mr-2"></i>
            <span>Hardcore & Qosidah</span>
          </div>
          <div class="flex items-center bg-white dark:bg-gray-800 px-4 py-2 rounded-full shadow-sm">
            <i class="fas fa-book text-blue-500 mr-2"></i>
            <span>Baca Buku</span>
          </div>
        </div>
      </div>
      <div class="rounded-lg overflow-hidden shadow-2xl bg-white dark:bg-gray-800 p-6 h-96 flex justify-center items-center relative">
        <div class="absolute inset-0 bg-[url('https://images.unsplash.com/photo-1555066931-4365d14bab8c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80')] bg-cover bg-center opacity-20"></div>
        <div class="relative z-10 w-full h-full flex flex-col items-center justify-center">
          <img src="https://media.giphy.com/media/l41lUeeCcb8Eg49vG/giphy.gif" alt="Ngoding Animasi" class="w-40 h-40 mb-6">
          <div class="text-center">
            <h3 class="text-xl font-semibold mb-2">"Kode adalah puisi logika"</h3>
            <p class="text-gray-600 dark:text-gray-400">Setiap baris punya cerita</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="reveal">
      <div class="text-center mb-12">
        <span class="text-sm font-semibold text-skyshine-dark dark:text-skyshine uppercase tracking-wider">Layanan</span>
        <h2 class="text-3xl font-bold mb-4 text-skyshine-dark dark:text-skyshine">Apa Yang Bisa Aku Bantu?</h2>
        <p class="max-w-2xl mx-auto text-lg">Saya menawarkan berbagai solusi teknologi untuk kebutuhan pribadi dan bisnis Anda.</p>
      </div>
      
      <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-gradient-to-br from-purple-200 via-indigo-200 to-skyshine-light dark:from-gray-800 dark:to-gray-700 rounded-xl p-6 shadow hover:shadow-lg transition hover:-translate-y-2 group">
          <div class="w-14 h-14 bg-white dark:bg-gray-900 rounded-lg flex items-center justify-center text-purple-600 dark:text-purple-400 text-2xl mb-4 group-hover:rotate-12 transition">
            <i class="fas fa-video"></i>
          </div>
          <h3 class="font-semibold text-lg mb-2">CCTV & Keamanan</h3>
          <p class="text-gray-600 dark:text-gray-400">Instalasi profesional sistem CCTV untuk rumah atau bisnis, termasuk konfigurasi remote monitoring.</p>
          <ul class="mt-3 space-y-1 text-sm text-gray-500 dark:text-gray-400">
            <li class="flex items-center"><i class="fas fa-check-circle text-green-500 mr-2"></i> Pemilihan kamera tepat</li>
            <li class="flex items-center"><i class="fas fa-check-circle text-green-500 mr-2"></i> Pemasangan rapi</li>
            <li class="flex items-center"><i class="fas fa-check-circle text-green-500 mr-2"></i> Aplikasi monitoring</li>
          </ul>
        </div>
        
        <div class="bg-gradient-to-br from-yellow-100 via-yellow-200 to-yellow-300 dark:from-gray-800 dark:to-gray-700 rounded-xl p-6 shadow hover:shadow-lg transition hover:-translate-y-2 group">
          <div class="w-14 h-14 bg-white dark:bg-gray-900 rounded-lg flex items-center justify-center text-yellow-600 dark:text-yellow-400 text-2xl mb-4 group-hover:rotate-12 transition">
            <i class="fas fa-network-wired"></i>
          </div>
          <h3 class="font-semibold text-lg mb-2">Jaringan Komputer</h3>
          <p class="text-gray-600 dark:text-gray-400">Desain dan implementasi jaringan LAN/WiFi yang stabil dan aman untuk berbagai skala kebutuhan.</p>
          <ul class="mt-3 space-y-1 text-sm text-gray-500 dark:text-gray-400">
            <li class="flex items-center"><i class="fas fa-check-circle text-green-500 mr-2"></i> Setup router/access point</li>
            <li class="flex items-center"><i class="fas fa-check-circle text-green-500 mr-2"></i> Troubleshooting jaringan</li>
            <li class="flex items-center"><i class="fas fa-check-circle text-green-500 mr-2"></i> Keamanan jaringan</li>
          </ul>
        </div>
        
        <div class="bg-gradient-to-br from-green-100 via-teal-100 to-skyshine-light dark:from-gray-800 dark:to-gray-700 rounded-xl p-6 shadow hover:shadow-lg transition hover:-translate-y-2 group">
          <div class="w-14 h-14 bg-white dark:bg-gray-900 rounded-lg flex items-center justify-center text-green-600 dark:text-green-400 text-2xl mb-4 group-hover:rotate-12 transition">
            <i class="fas fa-laptop-code"></i>
          </div>
          <h3 class="font-semibold text-lg mb-2">Servis Komputer</h3>
          <p class="text-gray-600 dark:text-gray-400">Perbaikan dan upgrade hardware/software untuk optimalisasi performa perangkat Anda.</p>
          <ul class="mt-3 space-y-1 text-sm text-gray-500 dark:text-gray-400">
            <li class="flex items-center"><i class="fas fa-check-circle text-green-500 mr-2"></i> Cleaning & maintenance</li>
            <li class="flex items-center"><i class="fas fa-check-circle text-green-500 mr-2"></i> Upgrade RAM/SSD</li>
            <li class="flex items-center"><i class="fas fa-check-circle text-green-500 mr-2"></i> Instalasi OS</li>
          </ul>
        </div>
        
        <div class="bg-gradient-to-br from-pink-100 via-red-100 to-skyshine-light dark:from-gray-800 dark:to-gray-700 rounded-xl p-6 shadow hover:shadow-lg transition hover:-translate-y-2 group">
          <div class="w-14 h-14 bg-white dark:bg-gray-900 rounded-lg flex items-center justify-center text-red-600 dark:text-red-400 text-2xl mb-4 group-hover:rotate-12 transition">
            <i class="fas fa-code"></i>
          </div>
          <h3 class="font-semibold text-lg mb-2">Web & Aplikasi</h3>
          <p class="text-gray-600 dark:text-gray-400">Pengembangan solusi digital custom dari front-end sampai back-end sesuai kebutuhan.</p>
          <ul class="mt-3 space-y-1 text-sm text-gray-500 dark:text-gray-400">
            <li class="flex items-center"><i class="fas fa-check-circle text-green-500 mr-2"></i> Website perusahaan</li>
            <li class="flex items-center"><i class="fas fa-check-circle text-green-500 mr-2"></i> Aplikasi custom</li>
            <li class="flex items-center"><i class="fas fa-check-circle text-green-500 mr-2"></i> Optimasi performa</li>
          </ul>
        </div>

        <!-- Tambahkan layanan IoT -->
        <div class="bg-gradient-to-br from-orange-100 via-amber-100 to-yellow-100 dark:from-gray-800 dark:to-gray-700 rounded-xl p-6 shadow hover:shadow-lg transition hover:-translate-y-2 group">
          <div class="w-14 h-14 bg-white dark:bg-gray-900 rounded-lg flex items-center justify-center text-orange-600 dark:text-orange-400 text-2xl mb-4 group-hover:rotate-12 transition">
            <i class="fas fa-robot"></i>
          </div>
          <h3 class="font-semibold text-lg mb-2">IoT & Embedded</h3>
          <p class="text-gray-600 dark:text-gray-400">Pengembangan sistem IoT berbasis Arduino, ESP32 dan mikrokontroler lainnya untuk otomasi dan monitoring.</p>
          <ul class="mt-3 space-y-1 text-sm text-gray-500 dark:text-gray-400">
            <li class="flex items-center"><i class="fas fa-check-circle text-green-500 mr-2"></i> Prototyping hardware</li>
            <li class="flex items-center"><i class="fas fa-check-circle text-green-500 mr-2"></i> Pemrograman C/C++</li>
            <li class="flex items-center"><i class="fas fa-check-circle text-green-500 mr-2"></i> Integrasi dengan cloud</li>
          </ul>
        </div>
      </div>
    </section>

    <!-- Skills Section -->
    <section id="skills" class="reveal">
      <div class="text-center mb-12">
        <span class="text-sm font-semibold text-skyshine-dark dark:text-skyshine uppercase tracking-wider">Keahlian</span>
        <h2 class="text-3xl font-bold mb-4 text-skyshine-dark dark:text-skyshine">Senjata Andalan</h2>
        <p class="max-w-2xl mx-auto text-lg">Alat dan teknologi yang saya kuasai untuk memberikan solusi terbaik.</p>
      </div>
      
      <div class="grid md:grid-cols-2 gap-10 items-center">
        <div class="grid grid-cols-2 gap-6">
          <!-- Tambahkan kartu IoT di sini -->
          <div class="bg-white dark:bg-gray-800 p-6 rounded-xl shadow hover:shadow-lg transition hover:-translate-y-1">
            <div class="text-4xl text-orange-500 mb-3">
              <i class="fas fa-microchip"></i>
            </div>
            <h3 class="font-semibold mb-2">IoT & Embedded</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">Arduino, ESP32, Raspberry Pi, C/C++</p>
          </div>
          
          <div class="bg-white dark:bg-gray-800 p-6 rounded-xl shadow hover:shadow-lg transition hover:-translate-y-1">
            <div class="text-4xl text-blue-500 mb-3">
              <i class="fab fa-html5"></i>
            </div>
            <h3 class="font-semibold mb-2">Frontend</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">HTML, CSS, JavaScript, Tailwind, React</p>
          </div>
          
          <div class="bg-white dark:bg-gray-800 p-6 rounded-xl shadow hover:shadow-lg transition hover:-translate-y-1">
            <div class="text-4xl text-green-500 mb-3">
              <i class="fab fa-node-js"></i>
            </div>
            <h3 class="font-semibold mb-2">Backend</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">Node.js, Express, PHP, MySQL, MongoDB</p>
          </div>
          
          <div class="bg-white dark:bg-gray-800 p-6 rounded-xl shadow hover:shadow-lg transition hover:-translate-y-1">
            <div class="text-4xl text-purple-500 mb-3">
              <i class="fas fa-tools"></i>
            </div>
            <h3 class="font-semibold mb-2">Tools</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">VS Code, Git, Docker, Postman</p>
          </div>
          
          <div class="bg-white dark:bg-gray-800 p-6 rounded-xl shadow hover:shadow-lg transition hover:-translate-y-1">
            <div class="text-4xl text-red-500 mb-3">
              <i class="fas fa-network-wired"></i>
            </div>
            <h3 class="font-semibold mb-2">Jaringan</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">CCTV, MikroTik, TP-Link, Ubiquiti</p>
          </div>
        </div>
        
        <div class="bg-white dark:bg-gray-800 p-8 rounded-xl shadow-xl">
          <h3 class="text-xl font-semibold mb-6 text-center">Level Keahlian</h3>
          <div class="space-y-5">
            <!-- Tambahkan progress bar untuk IoT -->
            <div>
              <div class="flex justify-between mb-1">
                <span>IoT Development</span>
                <span>88%</span>
              </div>
              <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                <div class="bg-orange-500 h-2.5 rounded-full" style="width: 88%"></div>
              </div>
            </div>
            
            <div>
              <div class="flex justify-between mb-1">
                <span>C/C++ Programming</span>
                <span>85%</span>
              </div>
              <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                <div class="bg-blue-700 h-2.5 rounded-full" style="width: 85%"></div>
              </div>
            </div>
            
            <div>
              <div class="flex justify-between mb-1">
                <span>HTML/CSS/JS</span>
                <span>95%</span>
              </div>
              <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                <div class="bg-blue-600 h-2.5 rounded-full" style="width: 95%"></div>
              </div>
            </div>
            
            <div>
              <div class="flex justify-between mb-1">
                <span>Node.js/Express</span>
                <span>85%</span>
              </div>
              <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                <div class="bg-green-600 h-2.5 rounded-full" style="width: 85%"></div>
              </div>
            </div>
            
            <div>
              <div class="flex justify-between mb-1">
                <span>Jaringan Komputer</span>
                <span>90%</span>
              </div>
              <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                <div class="bg-purple-600 h-2.5 rounded-full" style="width: 90%"></div>
              </div>
            </div>
            
            <div>
              <div class="flex justify-between mb-1">
                <span>CCTV Installation</span>
                <span>92%</span>
              </div>
              <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                <div class="bg-red-600 h-2.5 rounded-full" style="width: 92%"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Journey Timeline -->
    <section id="journey" class="reveal">
      <div class="text-center mb-12">
        <span class="text-sm font-semibold text-skyshine-dark dark:text-skyshine uppercase tracking-wider">Perjalanan</span>
        <h2 class="text-3xl font-bold mb-4 text-skyshine-dark dark:text-skyshine">Lintasan Teknologi</h2>
        <p class="max-w-2xl mx-auto text-lg">Dari masa ke masa, dari teknologi ke teknologi.</p>
      </div>
      
      <div class="relative">
        <!-- Timeline line -->
        <div class="hidden md:block absolute left-1/2 h-full w-1 bg-skyshine dark:bg-indigo-600 transform -translate-x-1/2"></div>
        
        <!-- Timeline items -->
        <div class="space-y-8 md:space-y-0">
          <!-- Item 1 -->
          <div class="relative md:flex justify-between items-center w-full">
            <div class="md:w-5/12 md:pr-8 md:text-right">
              <div class="p-6 bg-white dark:bg-gray-800 rounded-lg shadow hover:shadow-lg transition">
                <h3 class="text-xl font-bold text-skyshine-dark dark:text-skyshine">Awal Mula (1998-1999)</h3>
                <p class="text-gray-600 dark:text-gray-400">Jatuh cinta pada teknologi pertama kali dengan Windows 95 dan WordStar.</p>
              </div>
            </div>
            <div class="hidden md:flex items-center justify-center w-2/12">
              <div class="w-8 h-8 bg-skyshine-dark dark:bg-skyshine rounded-full shadow-lg flex items-center justify-center">
                <i class="fas fa-heart text-white"></i>
              </div>
            </div>
            <div class="md:w-5/12 md:pl-8">
              <div class="p-6 bg-gray-100 dark:bg-gray-700 rounded-lg shadow hover:shadow-lg transition">
                <div class="text-sm font-semibold text-gray-500 dark:text-gray-400">1998</div>
                <p>Pertama kali mengenal komputer dan mulai belajar dasar-dasar pemrograman, command prompt MSDOS.</p>
              </div>
            </div>
          </div>
          
          <!-- Item 2 -->
          <div class="relative md:flex justify-between items-center w-full md:flex-row-reverse">
            <div class="md:w-5/12 md:pl-8">
              <div class="p-6 bg-white dark:bg-gray-800 rounded-lg shadow hover:shadow-lg transition">
                <h3 class="text-xl font-bold text-skyshine-dark dark:text-skyshine">Era Database (2000-2005)</h3>
                <p class="text-gray-600 dark:text-gray-400">Menguasai dBASE, FoxPro dan Ms Access untuk aplikasi bisnis sederhana.</p>
              </div>
            </div>
            <div class="hidden md:flex items-center justify-center w-2/12">
              <div class="w-8 h-8 bg-skyshine-dark dark:bg-skyshine rounded-full shadow-lg flex items-center justify-center">
                <i class="fas fa-database text-white"></i>
              </div>
            </div>
            <div class="md:w-5/12 md:pr-8 md:text-right">
              <div class="p-6 bg-gray-100 dark:bg-gray-700 rounded-lg shadow hover:shadow-lg transition">
                <div class="text-sm font-semibold text-gray-500 dark:text-gray-400">2002</div>
                <p>Membuat aplikasi inventory pertama untuk usaha kecil menggunakan Ms Access.</p>
              </div>
            </div>
          </div>
          
          <!-- Item 3 -->
          <div class="relative md:flex justify-between items-center w-full">
            <div class="md:w-5/12 md:pr-8 md:text-right">
              <div class="p-6 bg-white dark:bg-gray-800 rounded-lg shadow hover:shadow-lg transition">
                <h3 class="text-xl font-bold text-skyshine-dark dark:text-skyshine">Jaringan & Hardware (2003-2010)</h3>
                <p class="text-gray-600 dark:text-gray-400">Memperdalam pengetahuan jaringan komputer dan perbaikan hardware.</p>
              </div>
            </div>
            <div class="hidden md:flex items-center justify-center w-2/12">
              <div class="w-8 h-8 bg-skyshine-dark dark:bg-skyshine rounded-full shadow-lg flex items-center justify-center">
                <i class="fas fa-network-wired text-white"></i>
              </div>
            </div>
            <div class="md:w-5/12 md:pl-8">
              <div class="p-6 bg-gray-100 dark:bg-gray-700 rounded-lg shadow hover:shadow-lg transition">
                <div class="text-sm font-semibold text-gray-500 dark:text-gray-400">2003</div>
                <p>Memulai bisnis kecil-kecilan servis komputer dan instalasi jaringan.</p>
              </div>
            </div>
          </div>
          
          <!-- Item 4 -->
          <div class="relative md:flex justify-between items-center w-full md:flex-row-reverse">
            <div class="md:w-5/12 md:pl-8">
              <div class="p-6 bg-white dark:bg-gray-800 rounded-lg shadow hover:shadow-lg transition">
                <h3 class="text-xl font-bold text-skyshine-dark dark:text-skyshine">Modern Web (2015-Sekarang)</h3>
                <p class="text-gray-600 dark:text-gray-400">Beralih ke pengembangan web modern dengan JavaScript dan framework terkini.</p>
              </div>
            </div>
            <div class="hidden md:flex items-center justify-center w-2/12">
              <div class="w-8 h-8 bg-skyshine-dark dark:bg-skyshine rounded-full shadow-lg flex items-center justify-center">
                <i class="fas fa-code text-white"></i>
              </div>
            </div>
            <div class="md:w-5/12 md:pr-8 md:text-right">
              <div class="p-6 bg-gray-100 dark:bg-gray-700 rounded-lg shadow hover:shadow-lg transition">
                <div class="text-sm font-semibold text-gray-500 dark:text-gray-400">2015</div>
                <p>Meluncurkan proyek Web App Koperasi untuk pegawai sekolah Pelayaran.</p>
              </div>
            </div>
          </div>

          <!-- Item 5 - Tambahan timeline untuk IoT -->
          <div class="relative md:flex justify-between items-center w-full">
            <div class="md:w-5/12 md:pr-8 md:text-right">
              <div class="p-6 bg-white dark:bg-gray-800 rounded-lg shadow hover:shadow-lg transition">
                <h3 class="text-xl font-bold text-skyshine-dark dark:text-skyshine">Era IoT & Embedded (2023-Sekarang)</h3>
                <p class="text-gray-600 dark:text-gray-400">Mengembangkan solusi IoT untuk otomasi rumah dan industri menggunakan Arduino, ESP32 dan Raspberry Pi.</p>
              </div>
            </div>
            <div class="hidden md:flex items-center justify-center w-2/12">
              <div class="w-8 h-8 bg-skyshine-dark dark:bg-skyshine rounded-full shadow-lg flex items-center justify-center">
                <i class="fas fa-robot text-white"></i>
              </div>
            </div>
            <div class="md:w-5/12 md:pl-8">
              <div class="p-6 bg-gray-100 dark:bg-gray-700 rounded-lg shadow hover:shadow-lg transition">
                <div class="text-sm font-semibold text-gray-500 dark:text-gray-400">2023</div>
                <p>Membuat sistem monitoring kebun otomatis pertama berbasis ESP32 dengan sensor kelembaban tanah.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Projects Showcase -->
    <section id="projects" class="reveal">
      <div class="text-center mb-12">
        <span class="text-sm font-semibold text-skyshine-dark dark:text-skyshine uppercase tracking-wider">Portofolio</span>
        <h2 class="text-3xl font-bold mb-4 text-skyshine-dark dark:text-skyshine">Proyek Terkini</h2>
        <p class="max-w-2xl mx-auto text-lg">Beberapa proyek IoT dan embedded system yang telah saya kerjakan.</p>
      </div>
      
      <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        <div class="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition transform hover:-translate-y-2">
          <div class="h-48 bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
            <i class="fas fa-temperature-low text-5xl text-orange-500"></i>
          </div>
          <div class="p-6">
            <h3 class="font-bold text-xl mb-2">Smart Greenhouse</h3>
            <p class="text-gray-600 dark:text-gray-400 mb-4">Sistem monitoring otomatis untuk rumah kaca berbasis ESP32 dengan sensor suhu, kelembaban, dan kontrol penyiraman.</p>
            <div class="flex flex-wrap gap-2">
              <span class="px-3 py-1 bg-orange-100 dark:bg-gray-700 text-orange-800 dark:text-orange-400 text-xs rounded-full">ESP32</span>
              <span class="px-3 py-1 bg-blue-100 dark:bg-gray-700 text-blue-800 dark:text-blue-400 text-xs rounded-full">C++</span>
              <span class="px-3 py-1 bg-green-100 dark:bg-gray-700 text-green-800 dark:text-green-400 text-xs rounded-full">IoT</span>
            </div>
          </div>
        </div>
        
        <div class="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition transform hover:-translate-y-2">
          <div class="h-48 bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
            <i class="fas fa-home text-5xl text-blue-500"></i>
          </div>
          <div class="p-6">
            <h3 class="font-bold text-xl mb-2">Home Automation</h3>
            <p class="text-gray-600 dark:text-gray-400 mb-4">Sistem otomasi rumah dengan Arduino Mega yang mengontrol lampu, pintu garasi, dan perangkat listrik lainnya.</p>
            <div class="flex flex-wrap gap-2">
              <span class="px-3 py-1 bg-orange-100 dark:bg-gray-700 text-orange-800 dark:text-orange-400 text-xs rounded-full">Arduino</span>
              <span class="px-3 py-1 bg-blue-100 dark:bg-gray-700 text-blue-800 dark:text-blue-400 text-xs rounded-full">C</span>
              <span class="px-3 py-1 bg-purple-100 dark:bg-gray-700 text-purple-800 dark:text-purple-400 text-xs rounded-full">MQTT</span>
            </div>
          </div>
        </div>
        
        <div class="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition transform hover:-translate-y-2">
          <div class="h-48 bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
            <i class="fas fa-gamepad text-5xl text-red-500"></i>
          </div>
          <div class="p-6">
            <h3 class="font-bold text-xl mb-2">PlaySphere</h3>
            <p class="text-gray-600 dark:text-gray-400 mb-4">Sistem billing timer Konsol Playstation berbasis NodeMCU dengan dashboard monitoring.</p>
            <div class="flex flex-wrap gap-2">
              <span class="px-3 py-1 bg-orange-100 dark:bg-gray-700 text-orange-800 dark:text-orange-400 text-xs rounded-full">ESP8266</span>
              <span class="px-3 py-1 bg-blue-100 dark:bg-gray-700 text-blue-800 dark:text-blue-400 text-xs rounded-full">JavaScript</span>
              <span class="px-3 py-1 bg-green-100 dark:bg-gray-700 text-green-800 dark:text-green-400 text-xs rounded-full">PHP</span>
			  <span class="px-3 py-1 bg-blue-100 dark:bg-gray-700 text-blue-800 dark:text-blue-400 text-xs rounded-full">C++</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Testimonials -->
    <section class="reveal">
      <div class="text-center mb-12">
        <span class="text-sm font-semibold text-skyshine-dark dark:text-skyshine uppercase tracking-wider">Kata Mereka</span>
        <h2 class="text-3xl font-bold mb-4 text-skyshine-dark dark:text-skyshine">Testimoni Klien</h2>
      </div>
      
      <div class="grid md:grid-cols-3 gap-8">
        <div class="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg hover:shadow-xl transition">
          <div class="flex items-center mb-4">
            <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Testimoni 1" class="w-12 h-12 rounded-full mr-4">
            <div>
              <h4 class="font-semibold">Budi Santoso</h4>
              <p class="text-sm text-gray-500 dark:text-gray-400">Pemilik Toko Elektronik</p>
            </div>
          </div>
          <p class="text-gray-600 dark:text-gray-400 italic">"Mas Daus membantu setup jaringan toko saya yang sebelumnya sering down. Sekarang lancar dan bisa monitor via HP. Profesional dan ramah!"</p>
          <div class="mt-4 flex text-yellow-400">
            <i class="fas fa-star"></i>
            <i class="fas fa-star"></i>
            <i class="fas fa-star"></i>
            <i class="fas fa-star"></i>
            <i class="fas fa-star"></i>
          </div>
        </div>
        
        <div class="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg hover:shadow-xl transition">
          <div class="flex items-center mb-4">
            <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="Testimoni 2" class="w-12 h-12 rounded-full mr-4">
            <div>
              <h4 class="font-semibold">Dewi Lestari</h4>
              <p class="text-sm text-gray-500 dark:text-gray-400">Pemilik Cafe</p>
            </div>
          </div>
          <p class="text-gray-600 dark:text-gray-400 italic">"CCTV yang dipasang Mas Daus sangat membantu keamanan cafe saya. Gambar jernih dan bisa diakses dari mana saja. Terima kasih!"</p>
          <div class="mt-4 flex text-yellow-400">
            <i class="fas fa-star"></i>
            <i class="fas fa-star"></i>
            <i class="fas fa-star"></i>
            <i class="fas fa-star"></i>
            <i class="fas fa-star-half-alt"></i>
          </div>
        </div>
        
        <div class="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg hover:shadow-xl transition">
          <div class="flex items-center mb-4">
            <img src="https://randomuser.me/api/portraits/men/75.jpg" alt="Testimoni 3" class="w-12 h-12 rounded-full mr-4">
            <div>
              <h4 class="font-semibold">Agus Setiawan</h4>
              <p class="text-sm text-gray-500 dark:text-gray-400">Pemilik UMKM</p>
            </div>
          </div>
          <p class="text-gray-600 dark:text-gray-400 italic">"Website yang dibuat Mas Daus sangat membantu promosi usaha saya. Desainnya bagus dan mudah diupdate sendiri. Recommended!"</p>
          <div class="mt-4 flex text-yellow-400">
            <i class="fas fa-star"></i>
            <i class="fas fa-star"></i>
            <i class="fas fa-star"></i>
            <i class="fas fa-star"></i>
            <i class="fas fa-star"></i>
          </div>
        </div>

        <!-- Tambahkan testimoni IoT -->
        <div class="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg hover:shadow-xl transition">
          <div class="flex items-center mb-4">
            <img src="https://randomuser.me/api/portraits/men/65.jpg" alt="Testimoni IoT" class="w-12 h-12 rounded-full mr-4">
            <div>
              <h4 class="font-semibold">Rudi Hartono</h4>
              <p class="text-sm text-gray-500 dark:text-gray-400">Pemilik GreenHouse</p>
            </div>
          </div>
          <p class="text-gray-600 dark:text-gray-400 italic">"Sistem monitoring tanaman berbasis ESP32 yang dibuat Mas Daus sangat membantu. Sekarang saya bisa pantau kelembaban tanah dari HP. Hasil panen meningkat 30%!"</p>
          <div class="mt-4 flex text-yellow-400">
            <i class="fas fa-star"></i>
            <i class="fas fa-star"></i>
            <i class="fas fa-star"></i>
            <i class="fas fa-star"></i>
            <i class="fas fa-star"></i>
          </div>
        </div>
      </div>
    </section>

    <!-- Quote Section -->
    <section class="text-center reveal">
      <div class="max-w-4xl mx-auto bg-white dark:bg-gray-800 p-8 rounded-xl shadow-lg">
        <blockquote class="text-xl md:text-2xl italic text-gray-700 dark:text-gray-300">
          "Teknologi bukan soal kecepatan... tapi bagaimana kau bisa tetap manusia saat menjalaninya. Di antara deretan kode dan kabel, jangan lupa untuk sesekali menengadah ke langit, menghirup kopi, dan tersenyum."
          <span class="block mt-6 font-semibold not-italic">— Mas Daus</span>
        </blockquote>
        <div class="mt-6 animate-wave inline-block">
          <i class="fas fa-hand-peace text-3xl text-skyshine-dark dark:text-skyshine"></i>
        </div>
      </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="reveal">
      <div class="bg-gradient-to-r from-skyshine-light to-skyshine dark:from-gray-800 dark:to-gray-700 rounded-2xl shadow-xl overflow-hidden">
        <div class="grid md:grid-cols-2">
          <div class="p-8 md:p-12">
            <h2 class="text-3xl font-bold mb-4 text-skyshine-dark dark:text-white">Mari Bekerja Sama</h2>
            <p class="text-lg mb-6 dark:text-gray-300">Punya proyek atau pertanyaan? Silakan hubungi saya melalui formulir ini atau langsung melalui kontak di bawah.</p>
            
            <div class="space-y-4">
              <div class="flex items-start">
                <div class="flex-shrink-0 bg-skyshine dark:bg-indigo-600 rounded-lg p-3 text-white mr-4">
                  <i class="fas fa-envelope"></i>
                </div>
                <div>
                  <h4 class="font-semibold">Email</h4>
                  <p class="text-gray-600 dark:text-gray-400"><EMAIL></p>
                </div>
              </div>
              
              <div class="flex items-start">
                <div class="flex-shrink-0 bg-skyshine dark:bg-indigo-600 rounded-lg p-3 text-white mr-4">
                  <i class="fas fa-phone-alt"></i>
                </div>
                <div>
                  <h4 class="font-semibold">Telepon/WA</h4>
                  <p class="text-gray-600 dark:text-gray-400">+62 812 5292 1114</p>
                </div>
              </div>
              
              <div class="flex items-start">
                <div class="flex-shrink-0 bg-skyshine dark:bg-indigo-600 rounded-lg p-3 text-white mr-4">
                  <i class="fas fa-map-marker-alt"></i>
                </div>
                <div>
                  <h4 class="font-semibold">Lokasi</h4>
                  <p class="text-gray-600 dark:text-gray-400">Sidoarjo, Jawa Timur - Indonesia</p>
                </div>
              </div>
            </div>
            
            <div class="mt-8 flex space-x-4">
              <a href="#" class="w-10 h-10 rounded-full bg-white dark:bg-gray-800 flex items-center justify-center text-blue-500 hover:text-blue-700 shadow hover:shadow-md transition">
                <i class="fab fa-facebook-f"></i>
              </a>
              <a href="#" class="w-10 h-10 rounded-full bg-white dark:bg-gray-800 flex items-center justify-center text-sky-400 hover:text-sky-600 shadow hover:shadow-md transition">
                <i class="fab fa-twitter"></i>
              </a>
              <a href="#" class="w-10 h-10 rounded-full bg-white dark:bg-gray-800 flex items-center justify-center text-pink-600 hover:text-pink-800 shadow hover:shadow-md transition">
                <i class="fab fa-instagram"></i>
              </a>
              <a href="https://github.com/inidaus" target="_blank" class="w-10 h-10 rounded-full bg-white dark:bg-gray-800 flex items-center justify-center text-gray-800 dark:text-gray-300 hover:text-black dark:hover:text-white shadow hover:shadow-md transition">
                <i class="fab fa-github"></i>
              </a>
            </div>
          </div>
          
          <div class="bg-white dark:bg-gray-800 p-8 md:p-12">
            <form class="space-y-4">
              <div>
                <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Nama Anda</label>
                <input type="text" id="name" class="w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-skyshine focus:border-transparent dark:bg-gray-700 dark:text-white">
              </div>
              
              <div>
                <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Email</label>
                <input type="email" id="email" class="w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-skyshine focus:border-transparent dark:bg-gray-700 dark:text-white">
              </div>
              
              <div>
                <label for="subject" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Subjek</label>
                <input type="text" id="subject" class="w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-skyshine focus:border-transparent dark:bg-gray-700 dark:text-white">
              </div>
              
              <div>
                <label for="message" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Pesan</label>
                <textarea id="message" rows="4" class="w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-skyshine focus:border-transparent dark:bg-gray-700 dark:text-white"></textarea>
              </div>
              
              <button type="submit" class="w-full bg-skyshine-dark hover:bg-skyshine-darker text-white font-medium py-2 px-4 rounded-lg shadow hover:shadow-md transition flex items-center justify-center">
                <i class="fas fa-paper-plane mr-2"></i> Kirim Pesan
              </button>
            </form>
          </div>
        </div>
      </div>
    </section>
  </main>

  <footer class="bg-skyshine-light dark:bg-gray-800 py-8">
    <div class="container mx-auto px-6">
      <div class="flex flex-col md:flex-row justify-between items-center">
        <div class="mb-4 md:mb-0">
          <a href="#" class="text-xl font-bold text-skyshine-dark dark:text-skyshine flex items-center">
            <i class="fas fa-cloud-sun mr-2"></i> Langit Cerah
          </a>
          <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">Warung Kode Langit Inovasi</p>
        </div>
        
        <div class="flex flex-wrap justify-center gap-6">
          <a href="#about" class="text-gray-600 dark:text-gray-400 hover:text-skyshine-dark dark:hover:text-skyshine transition">Tentang</a>
          <a href="#services" class="text-gray-600 dark:text-gray-400 hover:text-skyshine-dark dark:hover:text-skyshine transition">Layanan</a>
          <a href="#skills" class="text-gray-600 dark:text-gray-400 hover:text-skyshine-dark dark:hover:text-skyshine transition">Keahlian</a>
          <a href="#journey" class="text-gray-600 dark:text-gray-400 hover:text-skyshine-dark dark:hover:text-skyshine transition">Perjalanan</a>
          <a href="#contact" class="text-gray-600 dark:text-gray-400 hover:text-skyshine-dark dark:hover:text-skyshine transition">Kontak</a>
        </div>
      </div>
      
      <div class="border-t border-gray-200 dark:border-gray-700 mt-8 pt-8 text-center">
        <p class="text-sm text-gray-500 dark:text-gray-400">&copy; 2025 Mas Daus. All rights reserved. Dibuat dengan <i class="fas fa-heart text-red-500"></i> di Sidoarjo.</p>
        <p class="mt-2 text-xs text-gray-400">
          <a href="#" class="hover:underline">Kebijakan Privasi</a> | 
          <a href="#" class="hover:underline">Syarat & Ketentuan</a>
        </p>
      </div>
    </div>
  </footer>

  <script>
    document.addEventListener('DOMContentLoaded', () => {
      ScrollReveal().reveal('.reveal', {
        duration: 1200,
        origin: 'bottom',
        distance: '60px',
        easing: 'ease-in-out',
        reset: false,
        delay: 200
      });
      
      // Smooth scrolling for anchor links
      document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
          e.preventDefault();
          document.querySelector(this.getAttribute('href')).scrollIntoView({
            behavior: 'smooth'
          });
        });
      });
    });
  </script>
</body>

</html>