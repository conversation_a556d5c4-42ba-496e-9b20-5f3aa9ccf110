# Langit Cerah - Portfolio Mas Daus

Portfolio website profesional untuk Mas Daus - WarkopStack Developer dengan fitur slider interaktif dan desain responsif.

## ✨ Fitur Utama

### 🎠 Slider Interaktif
- **Services Section**: Slider dengan 2 slide menampilkan layanan teknologi
- **Projects Showcase**: Slider dengan 2 slide menampilkan portofolio proyek
- **Testimonials**: Slider dengan 2 slide menampilkan testimoni klien
- **Auto-slide**: Otomatis berganti setiap 5 detik
- **Manual Navigation**: Tombol prev/next dan dot indicators
- **Touch Support**: Swipe gesture untuk mobile devices
- **Keyboard Support**: Arrow keys untuk navigasi

### 🎨 Desain Modern
- **Gradient Backgrounds**: 5 variasi gradient yang menarik
- **Card Hover Effects**: Animasi hover dengan transform dan shadow
- **Responsive Design**: Optimal di semua ukuran layar
- **Dark Mode**: Toggle dark/light theme
- **Smooth Animations**: Transisi halus dan loading effects

### 📱 Mobile Friendly
- **Touch Navigation**: Swipe untuk navigasi slider
- **Responsive Grid**: Layout menyesuaikan ukuran layar
- **Mobile Menu**: Hamburger menu untuk navigasi mobile
- **Optimized Images**: Gambar responsif dan cepat loading

## 🚀 Teknologi yang Digunakan

- **HTML5**: Struktur semantic dan modern
- **CSS3**: Flexbox, Grid, Animations, Gradients
- **JavaScript ES6**: Slider functionality, event handling
- **Tailwind CSS**: Utility-first CSS framework
- **Font Awesome**: Icon library
- **ScrollReveal**: Scroll animations
- **Google Fonts**: Typography

## 📂 Struktur File

```
masdaus2/
├── index.html          # File utama website
├── README.md          # Dokumentasi
└── assets/            # (opsional untuk gambar lokal)
```

## 🎯 Sections

1. **Hero Section**: Profil utama dengan animasi dan CTA buttons
2. **About Section**: Informasi personal dan hobi
3. **Services Section**: Layanan teknologi (SLIDER)
4. **Skills Section**: Keahlian teknis dan progress bars
5. **Journey Timeline**: Perjalanan karir teknologi
6. **Projects Showcase**: Portfolio proyek (SLIDER)
7. **Testimonials**: Testimoni klien (SLIDER)
8. **Quote Section**: Kutipan inspiratif
9. **Contact Section**: Form kontak dan informasi
10. **Footer**: Informasi tambahan dan social links

## ⚙️ Konfigurasi Slider

### Auto-slide Interval
```javascript
// Default: 5000ms (5 detik)
slider.autoSlideInterval = setInterval(() => {
  changeSlide(sliderName, 1);
}, 5000);
```

### Slider Configuration
```javascript
const sliders = {
  services: { currentSlide: 0, totalSlides: 2, autoSlideInterval: null },
  projects: { currentSlide: 0, totalSlides: 2, autoSlideInterval: null },
  testimonials: { currentSlide: 0, totalSlides: 2, autoSlideInterval: null }
};
```

## 🎨 Color Scheme

### Primary Colors
- **Skyshine Light**: `#e0f7fa`
- **Skyshine Default**: `#81d4fa`
- **Skyshine Dark**: `#0288d1`

### Secondary Colors
- **Kopi Light**: `#d7ccc8`
- **Kopi Default**: `#5d4037`
- **Kopi Dark**: `#3e2723`

### Gradient Backgrounds
1. `linear-gradient(135deg, #667eea 0%, #764ba2 100%)`
2. `linear-gradient(135deg, #f093fb 0%, #f5576c 100%)`
3. `linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)`
4. `linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)`
5. `linear-gradient(135deg, #fa709a 0%, #fee140 100%)`

## 📱 Responsive Breakpoints

- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px

## 🔧 Customization

### Mengubah Interval Auto-slide
```javascript
// Ubah nilai 5000 (5 detik) sesuai kebutuhan
startAutoSlide(sliderName) {
  slider.autoSlideInterval = setInterval(() => {
    changeSlide(sliderName, 1);
  }, 3000); // 3 detik
}
```

### Menambah Slide Baru
1. Tambahkan slide baru di HTML
2. Update `totalSlides` di JavaScript
3. Tambahkan dot indicator baru

### Mengubah Warna Tema
Edit variabel CSS di bagian `tailwind.config`:
```javascript
colors: {
  skyshine: {
    light: '#your-color',
    DEFAULT: '#your-color',
    dark: '#your-color'
  }
}
```

## 🌟 Fitur Accessibility

- **Keyboard Navigation**: Arrow keys untuk slider
- **Focus States**: Outline untuk elemen fokus
- **Alt Text**: Deskripsi gambar untuk screen reader
- **Semantic HTML**: Struktur HTML yang bermakna
- **Color Contrast**: Kontras warna yang baik

## 📞 Kontak

- **Email**: <EMAIL>
- **Phone**: +62 812 5292 1114
- **Location**: Sidoarjo, Jawa Timur
- **GitHub**: https://github.com/inidaus
- **Instagram**: https://instagram.com/inidaus

## 📄 License

© 2025 Mas Daus. All rights reserved.

---

**Dibuat dengan ❤️ dan ☕ di Sidoarjo**
