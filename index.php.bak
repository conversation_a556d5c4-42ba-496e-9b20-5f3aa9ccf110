<!DOCTYPE html>
<html lang="id">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Langit <PERSON></title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://unpkg.com/scrollreveal"></script>
  <script>
    tailwind.config = {
      darkMode: 'class',
      theme: {
        extend: {
          colors: {
            skyshine: {
              light: '#e0f7fa',
              DEFAULT: '#81d4fa',
              dark: '#0288d1'
            }
          },
          animation: {
            fadeIn: "fadeIn 2s ease-in-out"
          },
          keyframes: {
            fadeIn: {
              '0%': { opacity: 0 },
              '100%': { opacity: 1 }
            }
          }
        }
      }
    }
  </script>
</head>

<body class="bg-gradient-to-br from-skyshine-light via-white to-skyshine-light text-gray-800 dark:text-white font-sans animate-fadeIn dark:bg-gray-900 transition-all">
  <header class="relative bg-skyshine dark:bg-indigo-700 overflow-hidden">
    <div class="absolute inset-0 bg-[url('https://images.unsplash.com/photo-1506748686214-e9df14d4d9d0?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80')] bg-cover bg-center opacity-30 dark:opacity-20"></div>
    <div class="relative z-10 container mx-auto py-16 text-center text-white">
      <h1 class="text-5xl font-extrabold tracking-wider mb-4">Mas Daus</h1>
      <p class="text-lg">WarkopStack Developer | Code Barista | Penggerak Langit Inovasi</p>
      <img src="https://cdn-icons-png.flaticon.com/512/2922/2922510.png" alt="Profil Mas Daus" class="mx-auto mt-6 w-32 h-32 rounded-full border-4 border-white shadow-2xl">
      <button onclick="document.body.classList.toggle('dark')" class="mt-5 text-sm underline hover:text-gray-300">Toggle Dark Mode</button>
    </div>
  </header>

  <main class="container mx-auto px-6 py-14 space-y-24">
    <section class="grid md:grid-cols-2 gap-10 items-center reveal">
      <div>
        <h2 class="text-3xl font-bold mb-4 text-skyshine-dark">Tentang Mas Daus</h2>
        <p class="text-lg leading-relaxed">
          Aku bukan siapa-siapa, tapi pernah jatuh cinta pada <em>Windows 95</em> dan suara booting-nya. Di Sidoarjo aku tumbuh, dari papan ketik WordStar ke terminal VS Code. Hidupku bukan hanya baris demi baris kode, tapi juga aroma kopi, tenda gunung, dan langit penuh bintang.
        </p>
        <p class="mt-4 text-lg">
          Di antara hembusan angin gunung dan klik keyboard, aku membangun mimpi. Dari dBASE sampai Docker, dari Lotus 123 sampai Langit Inovasi. Setiap error adalah pelajaran, setiap bug adalah teka-teki semesta.
        </p>
      </div>
      <div class="rounded-lg overflow-hidden shadow-xl bg-white dark:bg-gray-800 p-6 h-64 flex justify-center items-center">
        <div class="w-full h-full flex items-center justify-center">
          <img src="https://media.giphy.com/media/l41lUeeCcb8Eg49vG/giphy.gif" alt="Ngoding Animasi" class="w-40 h-40">
        </div>
      </div>
    </section>

    <section class="reveal">
      <h2 class="text-3xl font-bold mb-6 text-skyshine-dark">Apa Yang Bisa Aku Bantu?</h2>
      <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-gradient-to-br from-purple-200 via-indigo-200 to-skyshine-light dark:from-gray-800 dark:to-gray-700 text-gray-800 dark:text-white rounded-xl p-6 shadow hover:shadow-lg transition">
          <h3 class="font-semibold text-lg mb-2">CCTV & Keamanan</h3>
          <p>Instalasi dan konfigurasi CCTV untuk rumah atau kantor.</p>
        </div>
        <div class="bg-gradient-to-br from-yellow-100 via-yellow-200 to-yellow-300 dark:from-gray-800 dark:to-gray-700 text-gray-800 dark:text-white rounded-xl p-6 shadow hover:shadow-lg transition">
          <h3 class="font-semibold text-lg mb-2">Jaringan Komputer</h3>
          <p>Setup dan troubleshooting jaringan LAN/WiFi.</p>
        </div>
        <div class="bg-gradient-to-br from-green-100 via-teal-100 to-skyshine-light dark:from-gray-800 dark:to-gray-700 text-gray-800 dark:text-white rounded-xl p-6 shadow hover:shadow-lg transition">
          <h3 class="font-semibold text-lg mb-2">Servis Komputer</h3>
          <p>Perbaikan dan upgrade hardware/software laptop & PC.</p>
        </div>
        <div class="bg-gradient-to-br from-pink-100 via-red-100 to-skyshine-light dark:from-gray-800 dark:to-gray-700 text-gray-800 dark:text-white rounded-xl p-6 shadow hover:shadow-lg transition">
          <h3 class="font-semibold text-lg mb-2">Web & Aplikasi</h3>
          <p>Meracik solusi digital custom, front-end sampai back-end.</p>
        </div>
      </div>
    </section>

    <section class="grid md:grid-cols-2 gap-10 items-center reveal">
      <div>
        <h2 class="text-3xl font-bold mb-4 text-skyshine-dark">Senjata Andalan</h2>
        <ul class="list-disc pl-6 text-lg">
          <li>⚡ <strong>VS Code</strong> – sarang inspirasi tempat ngoding santai tapi serius</li>
          <li>🧠 <strong>Notepad++</strong> – ringan, cepat, dan setia buat hal-hal kecil</li>
          <li>☕ <strong>Terminal</strong> – tempat merenung, nge-test, dan ngelawan bug</li>
        </ul>
      </div>
      <div class="rounded-lg overflow-hidden shadow-xl bg-white dark:bg-gray-800 p-6 flex items-center justify-center">
        <div class="w-32 h-32 flex items-center justify-center">
          <img src="https://media.giphy.com/media/YQitE4YNQNahy/giphy.gif" alt="Terminal VS Code" class="w-full h-full object-contain">
        </div>
      </div>
    </section>

    <section class="text-center reveal">
      <blockquote class="text-xl italic max-w-xl mx-auto border-l-4 border-skyshine-dark pl-6 text-gray-700 dark:text-gray-300">
        "Teknologi bukan soal kecepatan... tapi bagaimana kau bisa tetap manusia saat menjalaninya."
        <span class="block mt-3 font-semibold">— Mas Daus</span>
      </blockquote>
    </section>
  </main>

  <footer class="bg-skyshine-light dark:bg-gray-800 text-center py-6 text-sm dark:text-gray-400">
    <p>&copy; 2025 Mas Daus. Warung Kode Langit Inovasi.</p>
    <p class="mt-2">
      🔗 <a href="https://github.com/inidaus" target="_blank" class="underline text-skyshine-dark hover:text-blue-800 dark:text-indigo-400 dark:hover:text-indigo-200">GitHub: @inidaus</a>
    </p>
  </footer>

  <script>
    document.addEventListener('DOMContentLoaded', () => {
      ScrollReveal().reveal('.reveal', {
        duration: 1200,
        origin: 'bottom',
        distance: '60px',
        easing: 'ease-in-out',
        reset: false
      });
    });
  </script>
</body>

</html>
